const {
	admin,
	accesstoken,
	company,
	company_token,
	staff,
	customer,
	shipment_job,
	external_api,
	sequelize
} = require("../../database/schemas");
const moment = require("moment");
const { Op, literal } = require("sequelize");
const bcrypt = require("bcrypt");
const commonFunction = require("../../assets/common");


exports.companyInData = async (body) => {
	return await company.findOne({
		where: {
			company_id: body.company_id.key
		},
		attributes: [
			...COMMON_COMPANY_ATTRIBUTES2,
			[
				sequelize.literal(
					"(select company_key FROM `company_tokens` where company_id = company.company_id) "
				),
				"api_key",
			],
			[
				sequelize.literal(
					"(select isEnable FROM `company_tokens` where company_id = company.company_id) "
				),
				"api_status",
			],
		],
		raw: true,
	})
}

exports.CMSResetPassword = async (fields) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(fields.new_password, salt);
	let updatePwd = await staff.update(
		{ password: pwd, verification_code: null },
		{ where: { verification_code: fields.code } }
	);
	return updatePwd;
};


exports.getUserDetails = async (req) => {
	let access_token = req.headers["access_token"] || "access_token";
	let data = await commonFunction.jwtTokenVerify(access_token);

	if (!data) {
		return accesstoken.findOne({
			where: {
				access_token: access_token,
			},
		})
	}

	let user = await accesstoken.findOne({
		where: {
			access_token: access_token,
		},
	})

	return user;
}



exports.getCompnayDeatils = async (req) => {
	let user = await staff.findOne({
		where: {
			staff_id: req,
		},
	})
	return await user;
}

exports.notificationStatusChange = async (userDetails) => {
	if (userDetails.admin_id !== null) {
		let updateData = await admin.update(
			{ notificationStatus: 1 },
			{ where: { admin_id: userDetails.admin_id } }
		);
		return updateData;
	}
	else if (userDetails.staff_id !== null) {
		let updateData = await staff.update(
			{ notificationStatus: 1 },
			{ where: { staff_id: userDetails.staff_id } }
		);
		return updateData;
	}
	else {
		let updateData = await company.update(
			{ notificationStatus: 1 },
			{ where: { company_id: userDetails.company_id } }
		);
		return updateData;
	}

}

exports.adminSignInWithCompanyId = async (body) => {
	const companyData = await company
		.findOne({
			where: {
				company_id: body.company_id,
				is_deleted: "0",
			},
			attributes: [
				...COMMON_COMPANY_ATTRIBUTES2,
				[
					sequelize.literal(
						"(select company_key FROM `company_tokens` where company_id = company.company_id) "
					),
					"api_key",
				],
				[
					sequelize.literal(
						"(select isEnable FROM `company_tokens` where company_id = company.company_id) "
					),
					"api_status",
				],
				[
					sequelize.literal(
						"(select pdf_time_stamp_checked FROM `groups` where group_id = company.	group_id) "
					),
					"pdf_time_stamp_checked",
				],
			],
			raw: true,
		})
	return companyData
}

exports.signIn = async (body) => {
	const adminData = await admin
		.findOne({
			where: {
				email: body.email,
				// status: "Active",
			},
			raw: true,
		});

	if (adminData !== null) {
		return adminData
	}
	else {
		const companyData = await company
			.findOne({
				where: {
					email: body.email,
					is_deleted: "0",
					// status: "active",
				},
				attributes: [
					...COMMON_COMPANY_ATTRIBUTES2,
					[
						sequelize.literal(
							"(select company_key FROM `company_tokens` where company_id = company.company_id) "
						),
						"api_key",
					],
					[
						sequelize.literal(
							"(select isEnable FROM `company_tokens` where company_id = company.company_id) "
						),
						"api_status",
					],
					[
						sequelize.literal(
							"(select pdf_time_stamp_checked FROM `groups` where group_id = company.	group_id) "
						),
						"pdf_time_stamp_checked",
					],
				],
				raw: true,
			})
		if (companyData !== null) {
			return companyData;
		} else {
			return await staff.findOne({
				where: {
					email: body.email,
					is_deleted: "0",
					// status: "active",
				},
				include: {
					model: company,
					as: "staff_company",
					attributes: [
						"company_name",
						"email",
						"company_identity",
						[
							sequelize.literal(
								"(select pdf_time_stamp_checked FROM `groups` where group_id = staff_company.group_id) "
							),
							"pdf_time_stamp_checked",
						],
					],
					where: {
						is_deleted: "0",
						// status: "active"
					},
				},
				raw: true,
			});
		}

	}

};


exports.adminEditPassword = async (request, admin_id, userData) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(request.body.new_password, salt);
	let updateData = {
		password: pwd,
	};

	if (userData && userData.company_id) {
		return await company.update(updateData, {
			where: { company_id: userData.company_id },
		});
	}

	else {
		return await admin.update(updateData, {
			where: { admin_id: admin_id },
		});
	}
};


exports.updateVerificationToken = async (email, checkData) => {
	let newVerificationToken = 100000 + Math.floor(Math.random() * 900000);
	let updateData = {
		verification_code: newVerificationToken,
	};

	if (checkData.admin_id) {
		const data = await admin.update(updateData, { where: { email: email } });
	}
	else if (checkData.company_id && checkData.staff_id) {
		const data = await staff.update(updateData, { where: { email: email } });
	}
	else {
		const data = await company.update(updateData, { where: { email: email } });
	}
	return newVerificationToken;

};


exports.cmsverifyToken = async (code) => {
	let isVerify = await admin.count({
		where: {
			verification_code: code,
		},
	});
	return isVerify;
};


exports.verifyToken = async (body) => {
	if (body.adminId) {
		let isVerify = await admin.count({
			where: {
				verification_code: body.code,
			},
		});
		return isVerify;
	}

	else if (body.companyId && body.staffId) {

		let isVerify = await staff.count({
			where: {
				verification_code: body.code,
			},
		});
		return isVerify;

	}
	else {

		let isVerify = await company.count({
			where: {
				verification_code: body.code,
			},
		});
		return isVerify;

	}

};


exports.resetPassword = async (fields) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(fields.new_password, salt);

	if (fields.adminId) {
		let updatePwd = await admin.update(
			{ password: pwd, verification_code: null },
			{ where: { verification_code: fields.code } }
		);
		return updatePwd;

	}
	else if (fields.companyId && fields.staffId) {

		let findStaffEmail = await staff.findOne({
			where: {
				staff_id: fields.staffId
			},
			attributes: ["email"]
		})

		let findCompanyEmail = await company.findOne({
			where: {
				email: findStaffEmail.email
			},
			attributes: ["email"]
		})

		if (findCompanyEmail) {
			let updatePwd = await company.update(
				{ password: pwd, verification_code: null },
				{ where: { email: findCompanyEmail.email } }
			);
		}


		let updatePwd = await staff.update(
			{ password: pwd, verification_code: null },
			{ where: { verification_code: fields.code } }
		);
		return updatePwd;

	}
	else {
		let findCompanyEmail = await company.findOne({
			where: {
				company_id: fields.companyId
			},
			attributes: ["email"]
		})
		let findStaffEmail = await staff.findOne({
			where: {
				email: findCompanyEmail.email
			},
			attributes: ["email"]
		})
		if (findStaffEmail) {
			let updatePwd = await staff.update(
				{ password: pwd, verification_code: null },
				{ where: { email: findStaffEmail.email } }
			);
		}
		let updatePwd = await company.update(
			{ password: pwd, verification_code: null },
			{ where: { verification_code: fields.code } }
		);
		return updatePwd;

	};
}


exports.countForDashboard = async (companyId, request) => {
	const orderBy = request.orderBy ? request.orderBy : "created_at";
	const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
	const search = request.search ? request.search : "";
	const dateFilter = request.dateFilter;

	if (!companyId || companyId === "" || companyId === "null" || companyId === "-1" || companyId === -1) {
		let companiesList = await company.findAll({
			order: [[orderBy, orderSequence]],
			where: {
				created_at: { [Op.between]: dateFilter },
				is_deleted: 0,
				[Op.or]: [
					{ company_name: { [Op.like]: "%" + search + "%" } },
					{ company_identity: { [Op.like]: "%" + search + "%" } },
					{ email: { [Op.like]: "%" + search + "%" } },
				],
				roles: { [Op.not]: "COMPANYSUPERADMIN" }
			},
			attributes: [
				"company_name",
				"company_id",
				"phone",
				"company_identity",
				"email",
				[
					sequelize.literal(
						'(select count(customer_id) FROM `customers` where company_id = company.company_id and is_deleted = "0")'
					),
					"total_customers",
				],
				[
					sequelize.literal(
						"(select count(shipment_job_id) FROM `shipment_jobs` where company_id = company.company_id and deletedAt IS NULL and shipment_name NOT REGEXP '(^|[[:space:]])TEST([[:space:]]|$)')"
					),
					"total_shipments",
				],
				[
					sequelize.literal(
						"(SELECT count(shipment_job_id) FROM `shipment_jobs` WHERE company_id = company.company_id and MONTH(created_at) = MONTH(now()) and YEAR(created_at) = YEAR(now()) and deletedAt IS NULL and shipment_name NOT REGEXP '(^|[[:space:]])TEST([[:space:]]|$)')"
					),
					"total_shipments_current_month",
				],
				[
					sequelize.literal(
						"(SELECT count(shipment_job_id) FROM `shipment_jobs` WHERE  company_id = company.company_id and MONTH( created_at ) = MONTH( DATE_SUB(CURDATE(),INTERVAL 1 MONTH )) AND YEAR( created_at ) = YEAR( DATE_SUB(CURDATE( ),INTERVAL 1 MONTH )) and deletedAt IS NULL and shipment_name NOT REGEXP '(^|[[:space:]])TEST([[:space:]]|$)')"
					),
					"total_shipments_last_month",
				],
			],
		})
		let shipmentList = await shipment_job.findAll({
			// order: [[orderBy, orderSequence]],
			where: {
				created_at: { [Op.between]: dateFilter },
			},
			attributes: [
				"shipment_job_id",
				"shipment_name",
				[
					sequelize.fn('date_format', sequelize.col('created_at'), '%Y-%m-%d'),
					'created_at'
				],
				[
					sequelize.literal(`(CASE WHEN is_job_complete_flag = '1' THEN 'YES' ELSE 'NO' END)`),
					"is_job_complete_flag",
				],
				[
					sequelize.literal(
						'(select company_name FROM `companies` where company_id = shipment_job.company_id)'
					),
					"company_name",
				],
			],
		})
		let companiesListChart = await company.count({
			where: {
				is_deleted: 0,
				roles: { [Op.not]: "COMPANYSUPERADMIN" }
			},
			attributes: [
				[sequelize.fn("YEAR", sequelize.col("created_at")), "year"],
				[sequelize.fn("MONTHNAME", sequelize.col("created_at")), "monthname"],
				[sequelize.fn("WEEK", sequelize.col("created_at")), "week"],
			],
			group: ["week", "monthname", "year"],
			order: ["created_at", "ASC"]
		})
		let { count: companiesTotal } = await company.findAndCountAll({
			where: { is_deleted: "0" },
		});
		let { count: companiesActive } = await company.findAndCountAll({
			where: { status: "active", is_deleted: "0" },
		});
		let { count: companiesInactive } = await company.findAndCountAll({
			where: { status: "inactive", is_deleted: "0" },
		});
		let { count: shipmentTotal } = await shipment_job.findAndCountAll({
			where: {
				deletedAt: null,
			},
		});
		let { count: shipmentActive } = await shipment_job.findAndCountAll({
			where: {
				is_job_complete_flag: 0,
				deletedAt: null,
			},
		});
		let { count: shipmentInactive } = await shipment_job.findAndCountAll({
			where: {
				is_job_complete_flag: 1,
				deletedAt: null,
			},
		});
		let { count: customersTotal } = await customer.findAndCountAll({
			where: { is_deleted: "0" },
		});
		let { count: customersActive } = await customer.findAndCountAll({
			where: {
				status: "active",
				is_deleted: "0",
			},
		});
		let { count: customersInactive } = await customer.findAndCountAll({
			where: {
				status: "inactive",
				is_deleted: "0",
			},
		});

		for (let i = 0; i <= companiesListChart.length - 1; i++) {
			companiesListChart[i].date = moment().year(companiesListChart[i].year).week(companiesListChart[i].week).startOf('week').format("MMM Do YYYY")
		}

		return {
			companiesListChart,
			companiesList,
			shipmentList,
			companiesTotal,
			companiesActive,
			companiesInactive,
			shipmentTotal,
			shipmentActive,
			shipmentInactive,
			customersTotal,
			customersActive,
			customersInactive,
		};
	} else {
		let { count: customersTotal } = await customer.findAndCountAll({
			where: {
				company_id: companyId,
				is_deleted: "0",
			},
		});
		let { count: customersActive } = await customer.findAndCountAll({
			where: {
				company_id: companyId,
				status: "active",
				is_deleted: "0",
			},
		});
		let { count: customersInactive } = await customer.findAndCountAll({
			where: {
				company_id: companyId,
				status: "inactive",
				is_deleted: "0",
			},
		});
		let { count: staffAdminTotal } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				roles: "ADMIN",
				is_deleted: "0",
			},
		});
		let { count: staffAdminActive } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				roles: "ADMIN",
				status: "active",
				is_deleted: "0",
			},
		});
		let { count: staffAdminInactive } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				roles: "ADMIN",
				status: "inactive",
				is_deleted: "0",
			},
		});
		let { count: staffWorkerTotal } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				roles: "WORKER",
				is_deleted: "0",
			},
		});
		let { count: staffWorkerActive } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				roles: "WORKER",
				status: "active",
				is_deleted: "0",
			},
		});
		let { count: staffWorkerInactive } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				roles: "WORKER",
				status: "inactive",
				is_deleted: "0",
			},
		});
		let { count: staffsTotal } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				is_deleted: "0",
			},
		});
		let { count: staffsActive } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				status: "active",
				is_deleted: "0",
			},
		});
		let { count: staffsInactive } = await staff.findAndCountAll({
			where: {
				company_id: companyId,
				status: "inactive",
				is_deleted: "0",
			},
		});
		let { count: shipmentTotal } = await shipment_job.findAndCountAll({
			where: {
				company_id: companyId,
				deletedAt: null,
			},
		});
		let { count: shipmentActive } = await shipment_job.findAndCountAll({
			where: {
				company_id: companyId,
				is_job_complete_flag: 0,
				deletedAt: null,
			},
		});
		let { count: shipmentInactive } = await shipment_job.findAndCountAll({
			where: {
				company_id: companyId,
				is_job_complete_flag: 1,
				deletedAt: null,
			},
		});
		return {
			staffsTotal,
			staffsActive,
			staffsInactive,
			staffsInactive,
			staffAdminTotal,
			staffAdminActive,
			staffAdminInactive,
			staffWorkerTotal,
			staffWorkerActive,
			staffWorkerInactive,
			customersTotal,
			customersActive,
			customersInactive,
			shipmentTotal,
			shipmentActive,
			shipmentInactive,
		};
	}
};

exports.accesstokenStorage = async (staff_id, request) => {
	let type = request.device_type ? request.device_type : "web";
	let newToken = await commonFunction.jwtTokenGenerate(staff_id);
	const data = await accesstoken.create(
		{
			staff_id: staff_id && staff_id,
			access_token: newToken,
			device_type: type,
			version: request.version,
		}
	);
	return data;
}

exports.accesstokenInsert = async (
	company_id,
	admin_id,
	staff_id,
	request,
	signInData
) => {
	let type = request.device_type ? request.device_type : "web";
	let newToken = await commonFunction.jwtTokenGenerate(admin_id);
	const name = company_id ? signInData.company_name : `${signInData.first_name} ${signInData.last_name}`;
	const data = await accesstoken.create(
		{
			admin_id: admin_id && admin_id,
			company_id: company_id && company_id,
			staff_id: staff_id && staff_id,
			name: name,
			access_token: newToken,
			device_type: type,
			version: request.version,
		}
	);
	return data;
};
exports.lisExternalApiUsers = async () => {
	return external_api.findAll();
};
exports.addExternalApiUser = async (body) => {
	let newToken = await commonFunction.jwtTokenGenerate(body.email);
	body["apiKey"] = newToken;
	const date = moment().add(1, "days");
	body["expire"] = date;
	const data = await external_api.create({ ...body });
	return data;
};
exports.addExternalApiUser = async (body) => {
	let newToken = await commonFunction.jwtTokenGenerate(body.email);
	body["apiKey"] = newToken;
	const date = moment().add(1, "days");
	body["expire"] = date;
	const data = await external_api.create({ ...body });
	return data;
};

exports.findApiUser = async (email) => {
	const data = await external_api.findOne({
		where: {
			email: email,
			status: "Active",
		},
	});
	return data;
};
exports.changeUserToken = async (email) => {
	let newToken = await commonFunction.jwtTokenGenerate(email);
	const date = moment().add(1, "days");
	const data = await external_api.update(
		{
			apiKey: newToken,
			expire: date,
			status: "Active",
		},
		{ where: { email: email } }
	);
	return data;
};

exports.findToken = async (email) => {
	//newChanges
	const data = await external_api
		.findOne({
			where: {
				email: email,
			},
		})
	if (data !== null) return data.apiKey;
	else return false;
};

exports.findAdminEmail = async (email) => {
	//newChanges
	const data = await admin
		.findOne({
			where: {
				email: email,
			},
		});

	if (data !== null) return true;
	else return false;
};
exports.findCompanyEmail = async (email) => {
	//newChanges
	const data = await company
		.findOne({
			where: {
				email: email,
			},
		})
	if (data !== null) return true;
	else return false;
};

exports.findStaffEmail = async (email) => {
	//newChanges
	const data = await staff
		.findOne({
			where: {
				email: email,
			},
		})
	if (data !== null) return true;
	else return false;
};

exports.findStaffDetailsEmail = async (email) =>
	staff
		.findOne({
			where: {
				email: email,
			},
		})




exports.findCustomerEmail = async (email) => {
	const data = await customer
		.findOne({
			where: {
				email: email,
			},
		})
	if (data !== null) return true;
	else return false;
};
