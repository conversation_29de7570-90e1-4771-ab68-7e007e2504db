const {
	app_logs
} = require("../../database/schemas");
const { Op } = require("sequelize");

exports.fetchActionLog = async (fieldsAndValues) => {
	const {
		start_date,
		end_date,
		page_size = 10,
		page_no = 1,
		search = ''
	} = fieldsAndValues;

	// Prepare date range filter if dates are provided
	const dateFilter = start_date && end_date
		? {
			created_at: {
				[Op.between]: [
					new Date(`${start_date}T00:00:00.000Z`),
					new Date(`${end_date}T23:59:59.999Z`)
				]
			}
		}
		: {};

	// Calculate pagination
	const limit = parseInt(page_size, 10);
	const offset = page_no > 1 ? (page_no - 1) * limit : 0;

	// Prepare search filter
	const searchFilter = search
		? {
			[Op.or]: [
				{ performed_by_name: { [Op.like]: `%${search}%` } },
				{ action_type: { [Op.like]: `%${search}%` } }
			]
		}
		: {};

	// Combine all filters
	const whereCondition = {
		...dateFilter,
		...searchFilter
	};

	return app_logs.findAndCountAll({
		limit,
		offset,
		where: whereCondition,
		order: [['created_at', 'DESC']]
	});
};


exports.createActionLog = async (request) => {
	return app_logs.create(request);
}


/**
 * Helper function to capture affected fields for CREATE operations
 * @param {Object} requestBody - The request body containing the fields
 * @param {Array} excludeFields - Fields to exclude from tracking (optional)
 * @returns {Object} Object containing key-value pairs of affected fields
 */
exports.captureAffectedFields = (requestBody, excludeFields = []) => {
	if (!requestBody || typeof requestBody !== 'object') {
		return {};
	}

	// Default fields to exclude from tracking
	const defaultExcludeFields = [
		'password',
		'confirm_password',
		'access_token',
		'refresh_token',
		'created_at',
		'updated_at',
		'is_deleted',
		"admin_id",
		"company_id",
		"staff_id",
		"customer_id"
	];

	const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

	// Create object with key-value pairs for affected fields
	const affectedFields = {};

	Object.keys(requestBody).forEach(field => {
		// Include field if it's not excluded and has a valid value
		if (!fieldsToExclude.includes(field) &&
			requestBody[field] !== undefined &&
			requestBody[field] !== null &&
			requestBody[field] !== '') {
			affectedFields[field] = requestBody[field];
		}
	});

	return affectedFields;
}

/**
 * Helper function to map request field names to database field names
 * @param {string} requestField - Field name from request body
 * @returns {string} Corresponding database field name
 */
const mapRequestFieldToDbField = (requestField) => {
	const fieldMapping = {
		// Item suggestion fields
		'item_name': 'name',
		'item_volume': 'volume',
		'item_weight': 'weight',
		'item_description': 'description',
		'item_status': 'status',

		// Tag fields
		'tag_name': 'name',
		'tag_for': 'tag_for',
		'tag_color': 'color',

		// Room fields
		'room_name': 'name',
		'room_status': 'status',

		// User/Staff fields
		'user_first_name': 'first_name',
		'user_last_name': 'last_name',
		'user_email': 'email',
		'user_phone': 'phone',
		'user_country_code': 'country_code',
		'user_roles': 'roles',
		'user_notes': 'notes',
		'user_status': 'status',

		// Customer fields
		'customer_first_name': 'first_name',
		'customer_last_name': 'last_name',
		'customer_email': 'email',
		'customer_phone': 'phone',
		'customer_country_code': 'country_code',
		'customer_notes': 'notes',
		'customer_status': 'status',

		// Shipment fields
		'shipment_name': 'shipment_name',
		'shipment_type_id': 'shipment_type_id',
		'pickup_date': 'pickup_date',
		'delivery_date': 'delivery_date',
		'pickup_address': 'pickup_address',
		'delivery_address': 'delivery_address',
		'notes': 'notes',
		'status': 'status',

		// Shipment Type fields
		'shipment_type_name': 'name',
		'number_of_stages': 'number_of_stages',
		'is_pickup_date_mandatory': 'is_pickup_date_mandatory',
		'is_make_user_mandatory': 'is_make_user_mandatory',

		// Shipment Type Stage fields
		'stage_name': 'name',
		'order_of_stages': 'order_of_stages',
		'scan_require': 'scan_require',
		'scan_out_of_storage': 'scan_out_of_storage',
		'signature_require': 'signature_require',
		'stage_status': 'status',

		// Shipment Type Stage of Shipment fields
		'local_stage_name': 'name',
		'local_order_of_stages': 'order_of_stages',
		'local_scan_require': 'scan_require',
		'local_scan_out_of_storage': 'scan_out_of_storage',
		'local_signature_require': 'signature_require',
		'local_stage_status': 'status'
	};

	return fieldMapping[requestField] || requestField;
}

/**
 * Helper function to capture both old and new values for UPDATE operations
 * @param {Object} oldData - The existing data before update
 * @param {Object} newData - The new data from request body
 * @param {Array} excludeFields - Fields to exclude from tracking (optional)
 * @returns {Object} Object containing old_values and new_values
 */
exports.captureUpdateFields = (oldData, newData, excludeFields = []) => {
	if (!newData || typeof newData !== 'object') {
		return {};
	}

	// Default fields to exclude from tracking
	const defaultExcludeFields = [
		'password',
		'confirm_password',
		'access_token',
		'refresh_token',
		'created_at',
		'updated_at',
		'is_deleted',
		"admin_id",
		"company_id",
		"staff_id",
		"customer_id"
	];

	const fieldsToExclude = [...defaultExcludeFields, ...excludeFields];

	const oldValues = {};
	const newValues = {};

	Object.keys(newData).forEach(field => {
		// Include field if it's not excluded and has a valid value
		if (!fieldsToExclude.includes(field) &&
			newData[field] !== undefined &&
			newData[field] !== null &&
			newData[field] !== '') {

			// Map request field to database field
			const dbField = mapRequestFieldToDbField(field);

			// Store old value using the mapped database field name
			oldValues[field] = oldData && oldData[dbField] !== undefined ? oldData[dbField] : null;

			// Store new value using the request field name
			newValues[field] = newData[field];
		}
	});

	return {
		old_values: oldValues,
		new_values: newValues
	};
}