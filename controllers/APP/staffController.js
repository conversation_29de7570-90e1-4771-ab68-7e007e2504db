const bcrypt = require("bcrypt");
const staffModel = require("../../models/APP/staffModel");
const companyModel = require("../../models/Admin/companyModel");

const commonFunction = require("../../assets/common");
const axios = require("axios");
const ActionLogModel = require("../../models/Admin/ActionLogModel");


exports.signIn = async function (request, response) {
	try {
		const userDetail = await staffModel.signIn(request.body);
		if ((userDetail && userDetail.roles === "SUPERADMIN") || (userDetail && userDetail.roles === "MAINADMIN")) {
			let passwordVerified = bcrypt.compareSync(
				request.body.password,
				userDetail.password
			);

			if (!passwordVerified) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: LOGIN_PASSWORD_FAIL,
					data: {},
				});
			} else {
				if (userDetail.status === "inactive") {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: USER_INACTIVE,
						data: {},
					});
				} else {
					try {
						const tokenDetail = await staffModel.accesstokenInsert(
							userDetail.staff_id,
							request.body,
							userDetail
						);
						userDetail["access_token"] = tokenDetail["access_token"];
						userDetail.photo !== "" && userDetail.photo !== null
							? userDetail.photo.includes("http")
								? (userDetail["user_profile_pic"] = userDetail.photo)
								: (userDetail["user_profile_pic"] =
									Const_AWS_BASE_Staff_Profile +
									"original/" +
									userDetail.photo)
							: "";

						ActionLogModel.createActionLog(
							{
								platform: "APP",
								performed_by_id: userDetail.staff_id,
								performed_by_name: `${userDetail.first_name} ${userDetail.last_name}`,
								performed_by_role: "User",
								action_type: "LOGIN",
							}
						)

						response.status(SUCCESS_CODE).json({
							status: 1,
							message: LOGIN_SUCCESS,
							data: userDetail,
						});
					} catch (error) {
						response.status(EXPECTATION_FAILED_CODE).json({
							status: 0,
							message: LOGIN_FAIL,
							data: {},
						});
					}
				}
			}
		}
		else if (userDetail) {
			const companyIdentityDetail = await staffModel.getCompanyIdentity(
				userDetail.company_id
			);
			if (
				companyIdentityDetail &&
				companyIdentityDetail.status === "inactive"
			) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: COMPANY_INACTIVE,
					data: {},
				});
			} else {
				let passwordVerified = bcrypt.compareSync(
					request.body.password,
					userDetail.password
				);

				if (!passwordVerified) {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: LOGIN_PASSWORD_FAIL,
						data: {},
					});
				} else {
					if (userDetail.status === "inactive") {
						response.status(EXPECTATION_FAILED_CODE).json({
							status: 0,
							message: USER_INACTIVE,
							data: {},
						});
					} else {
						try {
							const tokenDetail = await staffModel.accesstokenInsert(
								userDetail.staff_id,
								request.body,
								userDetail
							);
							userDetail["access_token"] = tokenDetail["access_token"];
							userDetail.photo !== "" && userDetail.photo !== null
								? userDetail.photo.includes("http")
									? (userDetail["user_profile_pic"] = userDetail.photo)
									: (userDetail["user_profile_pic"] =
										Const_AWS_BASE_Staff_Profile +
										"original/" +
										userDetail.photo)
								: "";

							ActionLogModel.createActionLog(
								{
									platform: "APP",
									performed_by_id: userDetail.staff_id,
									performed_by_name: `${userDetail.first_name} ${userDetail.last_name}`,
									performed_by_role: "User",
									action_type: "LOGIN",
								}
							)

							response.status(SUCCESS_CODE).json({
								status: 1,
								message: LOGIN_SUCCESS,
								data: userDetail,
							});
						} catch (error) {
							response.status(EXPECTATION_FAILED_CODE).json({
								status: 0,
								message: LOGIN_FAIL,
								data: {},
							});
						}
					}

				}
			}
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: LOGIN_EMAIL_FAIL,
				data: {},
			});
		}
	} catch (e) {
		console.log("exports.signIn -> error: ", e);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: e.message,
			data: {},
		});
	}
};

exports.signInWithcompanyId = async (request, response) => {
	try {
		const companyIdentityDetail = await staffModel.getCompanyIdentitySuperAdmin(
			request.body.company_id
		);
		if (!companyIdentityDetail) {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: "Please enter valid companyId.",
				data: {},
			});
		}
		else {
			const companyIdUser = await staffModel.companyIdUser(companyIdentityDetail)
			const tokenDetail = await staffModel.accesstokenInsert(
				companyIdUser.staff_id,
				request.body,
				companyIdUser
			);
			companyIdUser["access_token"] = tokenDetail["access_token"];
			companyIdUser["isLoginWithSuperAdmin"] = true
			companyIdUser.photo !== "" && companyIdUser.photo !== null
				? companyIdUser.photo.includes("http")
					? (companyIdUser["user_profile_pic"] = companyIdUser.photo)
					: (companyIdUser["user_profile_pic"] =
						Const_AWS_BASE_Staff_Profile +
						"original/" +
						companyIdUser.photo)
				: "";

			ActionLogModel.createActionLog(
				{
					platform: "APP",
					performed_by_id: companyIdUser.staff_id,
					performed_by_name: `${companyIdUser.first_name} ${companyIdUser.last_name}`,
					performed_by_role: "User",
					action_type: "LOGIN",
				}
			)

			response.status(SUCCESS_CODE).json({
				status: 1,
				message: LOGIN_SUCCESS,
				data: companyIdUser,
			});
		}
	}
	catch (error) {
		console.log("exports.signInWithcompanyId -> error: ", error);
		response.status(EXPECTATION_FAILED_CODE).json({
			status: 0,
			message: LOGOUT_FAIL,
			data: {},
		});
	}
}

exports.logout = async (request, response) => {
	try {
		const installedUserDetails = await staffModel.deleteAccessToken(request);
		if (installedUserDetails === 1) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: LOGOUT_SUCCESS,
				data: {},
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: LOGOUT_FAIL,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.logout -> error: ", error);
		response.status(EXPECTATION_FAILED_CODE).json({
			status: 0,
			message: LOGOUT_FAIL,
			data: {},
		});
	}
};

exports.changePasswordStorage = async (request, response) => {
	try {
		const { new_password, staffId } = request.body
		const editPasswordStorage = await staffModel.staffEditPasswordStorage(
			new_password,
			staffId
		);

		response.status(SUCCESS_CODE).json({
			status: 1,
			message: CHANGE_PASSWORD_SUCCESS,
			data: {},
		});
	}
	catch (error) {
		console.log("exports.changePasswordStorage -> error: ", error);
		response.status(EXPECTATION_FAILED_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.consumerLoginJsonFun = async (request, response, getCompanyDetails) => {
	const consumerLoginJson = JSON.stringify({
		companyIdTokenMoverInventory: getCompanyDetails.integration_key,
		email: "<EMAIL>",
		password: "5PLaRAqq",
		deviceToken: "abcd",
		deviceType: 0,
	});
	try {
		const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
			headers: {
				'Content-Type': 'application/json'
			}
		})
		if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginJson.data !== null) {
			return consumerLoginResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
		}

	} catch (error) {
		console.log("exports.consumerLoginJsonFun -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
	}
}


exports.staffPasswordJson = async (request, response, storage_staff_id, consumerLoginJson) => {
	try {
		const editPassword = JSON.stringify({
			userId: storage_staff_id,
			newPassword: request.body.new_password,
		});
		const editPasswordResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/change-password`, editPassword,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': consumerLoginJson.data.accessToken,
				}
			})

		if (editPasswordResponse.data !== "" && editPasswordResponse.data !== undefined && editPasswordResponse.data !== null) {
			return editPasswordResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Password update error 2", {});
		}

	} catch (error) {
		console.log("exports.staffPasswordJson -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Password update error 2", {});
	}
}

exports.changePassword = async (request, response) => {
	try {
		let headerData = await commonFunction.jwtTokenDecode(
			request.headers.access_token
		);
		const userDetail = await staffModel.getPassword(
			headerData.payload.user_id
		);

		if (userDetail) {
			let passwordVerified = bcrypt.compareSync(
				request.body.old_password,
				userDetail.password
			);
			if (!passwordVerified) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: OLD_PASSWORD_NOT_MATCH,
					data: {},
				});
			} else {
				const editPassword = await staffModel.staffEditPassword(
					request,
					headerData.payload.user_id
				);
				if (editPassword.includes(1)) {

					const findCompanyByEmail = await staffModel.findCompanyByEmail(userDetail.email);

					if (findCompanyByEmail) {
						const companyDetail = await staffModel.updatePasswordCompany(request.body, userDetail.email);
					}

					const deletedAccessTokens = await staffModel.deleteAccessTokens(
						request,
						headerData.payload.user_id
					);
					response.status(SUCCESS_CODE).json({
						status: 1,
						message: CHANGE_PASSWORD_SUCCESS,
						data: {},
					});
				} else {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: CHANGE_PASSWORD_FAIL,
						data: {},
					});
				}
			}
		}

	} catch (e) {
		console.log("exports.changePassword -> error: ", e);

		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: e.message,
			data: {},
		});
	}
};

exports.getStaffProfile = async (request, response) => {
	try {
		let headerData = await commonFunction.jwtTokenDecode(
			request.headers.access_token
		);
		let userDetails = await staffModel.getStaffProfile(
			headerData.payload.user_id
		);
		if (userDetails !== "") {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: USER_RETRIVED_SUCCESS,
				data: { userDetails },
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: USER_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.getStaffProfile -> error: ", error);
		response.status(EXPECTATION_FAILED_CODE).json({
			status: 0,
			message: USER_PROFILE_FAIL,
			data: {},
		});
	}
};

exports.forgotPassword = async (request, response) => {
	try {
		const emailCount = await staffModel.checkExistingEmail(
			request.body.email
		);
		if (emailCount <= 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: EMAIL_NOT_REGISTERED,
				data: {},
			});
		} else {
			let verificationToken = await staffModel.updateVerificationToken(
				request.body.email
			);
			const staffDetail = await staffModel.signIn(request.body);
			let buff = Buffer.from("'" + verificationToken + "'");
			let encToken = buff.toString("base64");

			let html = await commonFunction.readFile("ForgotPasswordApp.html");
			html = html.replace(/{{action_link}}/g, RESET_PASSWORD_LINK + encToken);
			html = html.replace(/{{name}}/g, staffDetail.first_name);
			html = html.replace(/{{otp}}/g, verificationToken);

			let isEmailSent = await commonFunction.sendEmail(
				request.body.email,
				"Reset Password",
				html
			);
			if (isEmailSent === false) {
				throw Error("Unable to send email");
			}
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: FORGOT_EMAIL_SENT,
				data: {},
			});
		}
	} catch (e) {
		console.log("exports.forgotPassword -> error: ", e);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: e.message,
			data: {},
		});
	}
};
