const shipmentModel = require("../../models/Admin/shipmentModel");
const shipmentTypeModel = require("../../models/Admin/shipmentTypeModel");
const commonModel = require("../../models/Admin/commonModel");
const companyModel = require("../../models/Admin/companyModel");
const staffModel = require("../../models/APP/staffModel");
const commonFunction = require("../../assets/common");
const axios = require("axios");
const ActionLogModel = require("../../models/Admin/ActionLogModel");

exports.consumerLoginJsonFun = async (request, response, getCompanyDetails) => {
	const consumerLoginJson = JSON.stringify({
		companyIdTokenMoverInventory: getCompanyDetails.integration_key,
		email: "<EMAIL>",
		password: "5PLaRAqq",
		deviceToken: "abcd",
		deviceType: 0,
	});
	try {
		const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
			headers: {
				'Content-Type': 'application/json'
			}
		})
		if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginResponse.data !== null) {
			return consumerLoginResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
	}
}

exports.AddShipmentJson = async (request, response, CustomerData, shipmentData, consumerLoginJson) => {

	function formatDate(inputDate) {
		const date = new Date(inputDate);
		const year = date.getUTCFullYear();
		const month = String(date.getUTCMonth() + 1).padStart(2, '0');
		const day = String(date.getUTCDate()).padStart(2, '0');
		return `${month}-${day}-${year}`;
	}

	const AddShipmentJsonData = JSON.stringify({
		shipmentName: shipmentData.shipment_name,
		customerId: CustomerData.storage_customer_id,
		warehouseId: shipmentData.warehouseId,
		contactReference: shipmentData.contact_reference,
		accountReference: shipmentData.account_reference,
		opportunityReference: shipmentData.opportunity_reference,
		estArrival: formatDate(shipmentData.pickup_date),
		estDelivery: formatDate(shipmentData.delivery_date),
		workOrderNotes: [

		],
		workOrderReference: shipmentData.wo_reference,
		externalReference: [

		],
		moveCoord: "",
		source: shipmentData.source,
		volume: shipmentData.estimated_volume,
		weight: shipmentData.estimated_weight,
		estUnits: "",
		originAddress: {
			addressLine1: shipmentData.pickup_address,
			addressLine2: shipmentData.pickup_address2,
			city: shipmentData.pickup_city,
			state: shipmentData.pickup_state,
			zipcode: shipmentData.pickup_zipcod,
			country: shipmentData.pickup_country,

		},
		destinationAddress: {
			addressLine1: shipmentData.delivery_address,
			addressLine2: shipmentData.delivery_address2,
			city: shipmentData.delivery_city,
			state: shipmentData.delivery_state,
			zipcode: shipmentData.delivery_zipcode,
			country: shipmentData.delivery_country,
		},
		importedTags: [],
		moverInventoryShipmentId: shipmentData.shipment_job_id,
		createdFromMoverInventory: true
	});

	try {
		const addShipmentResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/shipments`, AddShipmentJsonData, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (addShipmentResponse.status == 200) {
			return addShipmentResponse
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Add Shipment fail 1", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Add Shipment fail 2", {});
	}
}

exports.addShipmentTypeStage = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const findAllStagesOfShipment = await shipmentTypeModel.findAllStagesOfShipment(request.body);
		const totalStages = findAllStagesOfShipment.shipment_type_for_shipment.dataValues.total_stages
		const currentJobStages = findAllStagesOfShipment.shipment_type_for_shipment.dataValues.current_job_stage
		const localShipmentStage = findAllStagesOfShipment.shipment_type_for_shipment.dataValues.local_shipment_stage
		const addShipmentTypeStage = await shipmentTypeModel.addShipmentTypeStage(request.body);

		if (request.body.assign_storage_units_to_items || request.body.unassign_storage_units_from_items) {
			const checkShipmentAssignToStorage = await shipmentModel.checkShipmentAssignToStorage(request.body)
			if (checkShipmentAssignToStorage.storage_shipment_job_id == null || checkShipmentAssignToStorage.storage_shipment_job_id == undefined || checkShipmentAssignToStorage.storage_shipment_job_id == "") {
				const getCustomerStorageId = await companyModel.getCustomerStorageId(checkShipmentAssignToStorage.customer_id)
				const getIntegrationKeyToken = await companyModel.getIntegrationKeyToken(checkShipmentAssignToStorage.company_id)
				const consumerLoginJson = await this.consumerLoginJsonFun(request, response, getIntegrationKeyToken);
				const AddShipmentJson = await this.AddShipmentJson(request, response, getCustomerStorageId, checkShipmentAssignToStorage, consumerLoginJson);
				const updateShipmentStorageId = await shipmentModel.updateShipmentStorageId(checkShipmentAssignToStorage.shipment_job_id, AddShipmentJson.data.data.id)
			}
		}

		if (request.body.order_of_stages > totalStages) {
			const shipmentTypestagesupdate = await shipmentTypeModel.shipmentTypestagesupdate(addShipmentTypeStage);
			const shipmentJobCompleteFlagChange = await shipmentTypeModel.shipmentJobCompleteFlagChange(addShipmentTypeStage);
		}
		else {
			const findShipmentOrderStageDetails = await shipmentTypeModel.findShipmentOrderStageDetails(request.body);
			if (findShipmentOrderStageDetails.dataValues.unassign_storage_units_from_items == 1) {
				await shipmentTypeModel.updateNewStageAsScanOutOfStorage(addShipmentTypeStage.dataValues.local_shipment_stage_id);
				await shipmentTypeModel.removeOldStageAsScanOutOfStorage(findShipmentOrderStageDetails.dataValues.local_shipment_stage_id);
			}
			const shipmentTypeStageCountUpdate = await shipmentTypeModel.shipmentTypeStageCountUpdate(addShipmentTypeStage, totalStages);
			for (var i = request.body.order_of_stages; i <= totalStages; i++) {
				if (i == localShipmentStage[i - 1].dataValues.order_of_stages) {
					await shipmentTypeModel.updateShipmentTypeStageOrderNumber(localShipmentStage[i - 1].dataValues)
				}
			}
		}

		if (request.body.isJobComplete == 1 || request.body.isJobComplete == "1") {
			const jobCompleteFlag = await shipmentTypeModel.jobPendingFlagUpdate(addShipmentTypeStage, request.body.shipment_job_id);
		}

		if (addShipmentTypeStage) {
			// Capture affected fields for CREATE operation
			const affectedFields = ActionLogModel.captureAffectedFields(request.body);

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "SHIPMENT_TYPE_STAGE_OF_SHIPMENT_CREATE",
				action_performed_on_id: addShipmentTypeStage.dataValues.local_shipment_stage_id,
				action_performed_on_name: addShipmentTypeStage.dataValues.name,
				affected_fields: affectedFields
			})
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_TYPE_ADD_SUCCESS,
				data: {},
			});
		}
		else
			response.status(SERVER_ERROR_CODE).json({
				status: 0,
				message: SHIPMENT_TYPE_ADD_FAIL,
				data: {},
			});
	}
	catch (error) {
		console.log("exports.addShipmentTypeStage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}




exports.addShipmentType = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const addShipmentTypeData = await shipmentTypeModel.addShipmentType(
			request.body,
			getUserDetails
		);
		if (addShipmentTypeData) {
			// Capture affected fields for CREATE operation
			const affectedFields = ActionLogModel.captureAffectedFields(request.body);

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "SHIPMENT_TYPE_CREATE",
				action_performed_on_id: addShipmentTypeData.shipment_type_id,
				action_performed_on_name: addShipmentTypeData.name,
				affected_fields: affectedFields
			})
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_TYPE_ADD_SUCCESS,
				data: {},
			});
		}
		else
			response.status(SERVER_ERROR_CODE).json({
				status: 0,
				message: SHIPMENT_TYPE_ADD_FAIL,
				data: {},
			});
	} catch (error) {
		console.log("exports.addShipmentType -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.listShipmentType = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		let shipmentTypeList = await shipmentTypeModel.listShipmentType(
			request.body,
			getUserDetails
		);
		if (shipmentTypeList) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_TYPE_RETRIEVED_SUCCESS,
				data: { shipmentTypeList },
			});
		} else {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_TYPE_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.listShipmentType -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.listShipmentTypeStage = async (request, response) => {
	try {
		const shipmentTypeStageList =
			await shipmentTypeModel.listShipmentTypeStage(request.body);

		if (shipmentTypeStageList !== "") {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_TYPE_RETRIEVED_SUCCESS,
				data: { shipmentTypeStageList },
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: SHIPMENT_TYPE_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.listShipmentTypeStage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.editShipmentType = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		let getShipmentType = await shipmentTypeModel.findShipmentType(
			request.body,
		);
		if (getShipmentType) {
			// Capture both old and new values for UPDATE operation
			const affectedFields = ActionLogModel.captureUpdateFields(getShipmentType, request.body);

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "SHIPMENT_TYPE_UPDATE",
				action_performed_on_id: request.body.shipment_type_id,
				action_performed_on_name: request.body.name,
				affected_fields: affectedFields
			})
			let updateShipmentType = await shipmentTypeModel.updateShipmentType(
				request.body,
				getUserDetails
			);
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_TYPE_UPDATED_SUCCESS,
				data: {},
			});
		}
		else {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: SHIPMENT_TYPE_NOT_FOUND,
				data: {},
			});
		}

	} catch (error) {
		console.log("exports.editShipmentType -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};
exports.statusShipmentType = async (request, response) => {
	try {

		let getShipmentType = await shipmentTypeModel.findShipmentType(
			request.body
		);
		let getUserDetails = await commonModel.getUserDetails(request);

		if (getShipmentType) {
			let updateShipmentTypeStatus =
				await shipmentTypeModel.statusShipmentType(
					request.body
				);
			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: getShipmentType.status == "active" ? "SHIPMENT_TYPE_DEACTIVATE" : "SHIPMENT_TYPE_ACTIVATE",
				action_performed_on_id: request.body.shipment_type_id,
				action_performed_on_name: getShipmentType.name,
			})
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: getShipmentType.status == "active" ? "Shipment type deactivated successfully" : "Shipment type activated successfully",
				data: {},
			});
		} else {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: SHIPMENT_TYPE_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.statusShipmentType -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};
exports.deleteShipmentType = async (request, response) => {
	try {
		let getShipmentType = await shipmentTypeModel.findShipmentType(
			request.body
		);
		let getUserDetails = await commonModel.getUserDetails(request);
		if (getShipmentType) {
			let deleteShipmentTypeRes =
				await shipmentTypeModel.deleteShipmentType(request.body);
			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "SHIPMENT_TYPE_DELETE",
				action_performed_on_id: request.body.shipment_type_id,
				action_performed_on_name: getShipmentType.name,
			})
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_TYPE_DELETED_SUCCESS,
				data: {},
			});
		} else {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_TYPE_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.deleteShipmentType -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.changeShipmentStageStatus = async (request, response) => {
	try {
		const viewShipmentStage = await shipmentTypeModel.viewShipmentStage(
			request.body.shipment_stage_id
		);
		const shipmentStageDetail =
			await shipmentTypeModel.changeShipmentStageStatus(request.body);

		let getUserDetails = await commonModel.getUserDetails(request);

		if (shipmentStageDetail.includes(1)) {

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: viewShipmentStage.status == "active" ? "SHIPMENT_TYPE_STAGE_DEACTIVATE" : "SHIPMENT_TYPE_STAGE_ACTIVATE",
				action_performed_on_id: viewShipmentStage.shipment_stage_id,
				action_performed_on_name: viewShipmentStage.name,
			})

			response.status(SUCCESS_CODE).json({
				message: STATUS_CHANGE_SUCCESS,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: STATUS_CHANGE_FAIL,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.changeShipmentStageStatus -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.viewShipmentStage = async (request, response) => {
	try {
		const shipmentStageDetail = await shipmentTypeModel.viewShipmentStage(
			request.body.shipment_stage_id
		);
		const shipmentTypeDetail = await shipmentTypeModel.fetchShipmentTypeStagesForCompnay(shipmentStageDetail.dataValues.shipment_type_id);

		if (shipmentStageDetail !== "") {
			response.status(SUCCESS_CODE).json({
				message: SHIPMENT_STAGE_RETRIEVED_SUCCESS,
				data: shipmentStageDetail,
				shipmentTypeDetail
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: SHIPMENT_STAGE_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.viewShipmentStage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.viewShipmentType = async (request, response) => {
	try {
		const shipmentTypeDetail = await shipmentTypeModel.viewShipmentType(
			request.body.shipment_type_id
		);
		if (shipmentTypeDetail !== "") {
			response.status(SUCCESS_CODE).json({
				message: SHIPMENT_TYPE_RETRIEVED_SUCCESS,
				data: shipmentTypeDetail,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: SHIPMENT_TYPE_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.viewShipmentType -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.editShipmentStage = async (request, response) => {
	try {
		const { nextAdditionalStageId } = request.body;

		// Get old data before update
		const oldShipmentStageData = await shipmentTypeModel.viewShipmentStage(
			request.body.shipment_stage_id
		);

		const shipmentStageDetail = await shipmentTypeModel.editShipmentStage(
			request.body
		);
		let getUserDetails = await commonModel.getUserDetails(request);

		if (nextAdditionalStageId) {
			await shipmentTypeModel.deleteShipmentTypeStageModel(nextAdditionalStageId)
			const shipmentStageDetail = await shipmentTypeModel.viewShipmentStage(
				request.body.shipment_stage_id
			);
			const shipmentTypeDetail = await shipmentTypeModel.fetchShipmentTypeStagesForCompnay(shipmentStageDetail.dataValues.shipment_type_id);
			await shipmentTypeModel.shipmentTypeDetailLengthUpdate(shipmentStageDetail.dataValues.shipment_type_id, shipmentTypeDetail.length)

			async function deleteAndReorder(array, orderToDelete) {
				const indexToDelete = array.findIndex(item => item.order_of_stages === orderToDelete);
				if (indexToDelete !== -1) {
					array.splice(indexToDelete, 1);

					for (let i = indexToDelete; i < array.length; i++) {
						array[i].order_of_stages -= 1;
						await shipmentTypeModel.updateStageOrderNumber(array[i].shipment_stage_id, array[i].order_of_stages)
					}

				}
			}
			deleteAndReorder(shipmentTypeDetail, request.body.order_of_stages);
		}

		// Capture both old and new values for UPDATE operation
		const affectedFields = ActionLogModel.captureUpdateFields(oldShipmentStageData, request.body);

		ActionLogModel.createActionLog({
			platform: "CMS",
			performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
			performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
			performed_by_name: getUserDetails.name,
			action_type: "SHIPMENT_TYPE_STAGE_UPDATE",
			action_performed_on_id: request.body.shipment_stage_id,
			action_performed_on_name: request.body.name,
			affected_fields: affectedFields
		})
		if (shipmentStageDetail) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_STAGE_UPDATE_SUCCESS,
				data: {},
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: SHIPMENT_STAGE_UPDATE_FAIL,
				data: {},
			});
		}

	} catch (error) {
		console.log("exports.editShipmentStage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.basicShipmentTypeController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);

		request.query.search = request.query.search ? request.query.search : "";
		const shipmentTypeList = await shipmentTypeModel.basicShipmentTypeModel(request.query, getUserDetails);
		if (shipmentTypeList.length > 0) {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				SHIPMENT_TYPE_RETRIEVED_SUCCESS,
				shipmentTypeList
			);
		} else {
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				1,
				SHIPMENT_TYPE_NOT_FOUND,
				{}
			);
		}
	}
	catch (reason) {
		console.log("exports.basicShipmentTypeController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		);
	}
};

exports.ShipmentTypeListStorageController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		const getCompanyId = await companyModel.getCompanyIdForStaff(request.body);
		request.body.company_id = getCompanyId.company_id

		const shipmentTypeList = await shipmentTypeModel.ShipmentTypeListStorageModel(request.query, request.body);
		if (shipmentTypeList.length > 0) {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				SHIPMENT_TYPE_RETRIEVED_SUCCESS,
				shipmentTypeList
			);
		} else {
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				1,
				SHIPMENT_TYPE_NOT_FOUND,
				{}
			);
		}
	}
	catch (reason) {
		console.log("exports.ShipmentTypeListStorageController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		);
	}
};

exports.isValidShipmentTypeController = async (request, response, next) => {
	if (
		await shipmentTypeModel.isShipmentTypeModel(request.body.shipment_type_id)
	) {
		next();
	} else {
		commonFunction.generateResponse(
			response,
			NOT_FOUND_CODE,
			0,
			SHIPMENT_TYPE_NOT_FOUND,
			{}
		);
	}
};

exports.isValidShipmentStageController = async (request, response, next) => {
	let { altered_stage_id, current_job_stage } = request.body;
	let { stageId } = request.params;
	if (altered_stage_id === -1) {
		next();
	}
	else {
		let stage = stageId
			? stageId
			: altered_stage_id
				? altered_stage_id
				: current_job_stage;
		if (await shipmentTypeModel.isShipmentStageModel(stage)) {
			next();
		} else {
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				SHIPMENT_STAGE_NOT_FOUND,
				{}
			);
		}
	}
};

exports.isValidShipmentStageCheckController = async (request, response, next) => {
	let { current_job_stage } = request.body;
	const check = await shipmentTypeModel.isShipmentStageModel(current_job_stage)
	if (check) {
		next();
	} else {
		commonFunction.generateResponse(
			response,
			NOT_FOUND_CODE,
			0,
			SHIPMENT_STAGE_NOT_FOUND,
			{}
		);
	}
};

exports.checkShipmentTypeAssignToJob = async (request, response, next) => {
	const { count } = await shipmentTypeModel.checkShipmentTypeAssignToJob(
		request.body.shipment_type_id
	);
	if (count && count > 0) {
		commonFunction.generateResponse(
			response,
			EXPECTATION_FAILED_CODE,
			0,
			"Cannot delete this Shipment Type as it has Shipments associated!",
			{}
		);
	} else {
		next();
	}
};

exports.checkShipmentTypeAssignToInactiveJob = async (request, response, next) => {
	const { count } = await shipmentTypeModel.checkShipmentTypeAssignToJob(
		request.body.shipment_type_id
	);
	if (count && count > 0) {
		commonFunction.generateResponse(
			response,
			EXPECTATION_FAILED_CODE,
			0,
			"Cannot inactive this Shipment Type as it has Shipments associated!",
			{}
		);
	} else {
		next();
	}
};