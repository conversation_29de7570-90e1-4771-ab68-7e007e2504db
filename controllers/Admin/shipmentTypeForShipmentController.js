
const shipmentTypeForShipmentModel = require("../../models/Admin/shipmentTypeForShipmentModel");
const commonModel = require("../../models/Admin/commonModel");
const commonFunction = require("../../assets/common");
const companyModel = require("../../models/Admin/companyModel");
const shipmentModel = require("../../models/Admin/shipmentModel");
const axios = require("axios");
const ActionLogModel = require("../../models/Admin/ActionLogModel");

exports.consumerLoginJsonFun = async (request, response, getCompanyDetails) => {
	const consumerLoginJson = JSON.stringify({
		companyIdTokenMoverInventory: getCompanyDetails.integration_key,
		email: "<EMAIL>",
		password: "5PLaRAqq",
		deviceToken: "abcd",
		deviceType: 0,
	});
	try {
		const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
			headers: {
				'Content-Type': 'application/json'
			}
		})
		if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginResponse.data !== null) {
			return consumerLoginResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
	}
}

exports.AddShipmentJson = async (request, response, CustomerData, shipmentData, consumerLoginJson) => {

	function formatDate(inputDate) {
		const date = new Date(inputDate);
		const year = date.getUTCFullYear();
		const month = String(date.getUTCMonth() + 1).padStart(2, '0');
		const day = String(date.getUTCDate()).padStart(2, '0');
		return `${month}-${day}-${year}`;
	}

	const AddShipmentJsonData = JSON.stringify({
		shipmentName: shipmentData.shipment_name,
		customerId: CustomerData.storage_customer_id,
		warehouseId: shipmentData.warehouseId,
		contactReference: shipmentData.contact_reference,
		accountReference: shipmentData.account_reference,
		opportunityReference: shipmentData.opportunity_reference,
		estArrival: formatDate(shipmentData.pickup_date),
		estDelivery: formatDate(shipmentData.delivery_date),
		workOrderNotes: [

		],
		workOrderReference: shipmentData.wo_reference,
		externalReference: [

		],
		moveCoord: "",
		source: shipmentData.source,
		volume: shipmentData.estimated_volume,
		weight: shipmentData.estimated_weight,
		estUnits: "",
		originAddress: {
			addressLine1: shipmentData.pickup_address,
			addressLine2: shipmentData.pickup_address2,
			city: shipmentData.pickup_city,
			state: shipmentData.pickup_state,
			zipcode: shipmentData.pickup_zipcod,
			country: shipmentData.pickup_country,

		},
		destinationAddress: {
			addressLine1: shipmentData.delivery_address,
			addressLine2: shipmentData.delivery_address2,
			city: shipmentData.delivery_city,
			state: shipmentData.delivery_state,
			zipcode: shipmentData.delivery_zipcode,
			country: shipmentData.delivery_country,
		},
		importedTags: [],
		moverInventoryShipmentId: shipmentData.shipment_job_id,
		createdFromMoverInventory: true
	});

	try {
		const addShipmentResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/shipments`, AddShipmentJsonData, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (addShipmentResponse.status == 200) {
			return addShipmentResponse
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Add Shipment fail 1", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Add Shipment fail 2", {});
	}
}

exports.copyShipmentStagesForShipment = async (request, response) => {
	try {
		const findAllShipmentOfCompany = await shipmentTypeForShipmentModel.findAllShipmentOfCompany(request.body);
		if (findAllShipmentOfCompany !== "") {

			for (let y = 0; y < findAllShipmentOfCompany.rows.length; y++) {
				const findOldShipmentType = await shipmentTypeForShipmentModel.findOldShipment(findAllShipmentOfCompany.rows[y].shipment_type_id);
				const createNewShipmentType = await shipmentTypeForShipmentModel.createNewShipmentType(findOldShipmentType, findAllShipmentOfCompany.rows[y].shipment_job_id)
				const upShipment = await shipmentTypeForShipmentModel.upShipment(createNewShipmentType.local_shipment_type_id, findAllShipmentOfCompany.rows[y].shipment_job_id);
				let fetchShipmentTypeStagesForShipment = await shipmentTypeForShipmentModel.fetchShipmentTypeStagesForShipment(findAllShipmentOfCompany.rows[y].shipment_type_id);
				let createShipmentTypeStagesForShipment = await shipmentTypeForShipmentModel.createShipmentTypeStagesForShipment(fetchShipmentTypeStagesForShipment, createNewShipmentType);
				for (let i = 0; i < createShipmentTypeStagesForShipment.length; i++) {
					if (findAllShipmentOfCompany.rows[y].job_status == createShipmentTypeStagesForShipment[i].ref_shipment_stage_id) {
						const upShipmentStageUpdate = await shipmentTypeForShipmentModel.upShipmentStageUpdate(createShipmentTypeStagesForShipment[i].local_shipment_stage_id, findAllShipmentOfCompany.rows[y].shipment_job_id);
					}
				}
			}

			response.status(SUCCESS_CODE).json({
				message: SHIPMENT_STAGE_RETRIEVED_SUCCESS,
				data: "Done",
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: SHIPMENT_STAGE_NOT_FOUND,
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.copyShipmentStagesForShipment -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}


exports.copyisSccanedStageForShipmentInventory = async (request, response) => {
	try {
		const getAllScannedInventory = await shipmentTypeForShipmentModel.getAllScannedInventory();



		if (getAllScannedInventory !== "") {

			for (let y = 0; y < getAllScannedInventory.rows.length; y++) {
				const getAllShipmentStages = await shipmentTypeForShipmentModel.getAllShipmentStages(getAllScannedInventory.rows[y].shipment_job_id);
				for (let i = 0; i < getAllShipmentStages.rows.length; i++) {
					if (getAllScannedInventory.rows[y].shipment_job_id == getAllShipmentStages.rows[i].shipment_job_id && getAllScannedInventory.rows[y].current_stage_id == getAllShipmentStages.rows[i].ref_shipment_stage_id) {
						const upSccanedShipmentStageUpdate = await shipmentTypeForShipmentModel.upSccanedShipmentStageUpdate(getAllScannedInventory.rows[y].scan_status_id, getAllShipmentStages.rows[i].local_shipment_stage_id);

					}
				}
			}

			response.status(SUCCESS_CODE).json({
				message: SHIPMENT_STAGE_RETRIEVED_SUCCESS,
				data: "Done",
			});

		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: SHIPMENT_STAGE_NOT_FOUND,
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.copyisSccanedStageForShipmentInventory -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.viewShipmentForShipmentStage = async (request, response) => {
	try {
		const shipmentStageDetail = await shipmentTypeForShipmentModel.viewShipmentStage(
			request.body.local_shipment_stage_id
		);

		const shipmentTypeDetail = await shipmentTypeForShipmentModel.fetchShipmentTypegForShipmentStagesForCompnay(shipmentStageDetail.dataValues.shipment_job_id);
		if (shipmentStageDetail !== "") {
			response.status(SUCCESS_CODE).json({
				message: SHIPMENT_STAGE_RETRIEVED_SUCCESS,
				data: shipmentStageDetail,
				shipmentTypeDetail
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: SHIPMENT_STAGE_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.viewShipmentStage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};



exports.editShipmentStage = async (request, response) => {
	try {

		const { nextAdditionalStageId } = request.body;

		let getUserDetails = await commonModel.getUserDetails(request);

		if (request.body.assign_storage_units_to_items || request.body.unassign_storage_units_from_items) {
			const checkShipmentAssignToStorage = await shipmentModel.checkShipmentAssignToStorage(request.body)
			if (checkShipmentAssignToStorage.storage_shipment_job_id == null || checkShipmentAssignToStorage.storage_shipment_job_id == undefined || checkShipmentAssignToStorage.storage_shipment_job_id == "") {
				const getCustomerStorageId = await companyModel.getCustomerStorageId(checkShipmentAssignToStorage.customer_id)
				const getIntegrationKeyToken = await companyModel.getIntegrationKeyToken(checkShipmentAssignToStorage.company_id)
				const consumerLoginJson = await this.consumerLoginJsonFun(request, response, getIntegrationKeyToken);
				const AddShipmentJson = await this.AddShipmentJson(request, response, getCustomerStorageId, checkShipmentAssignToStorage, consumerLoginJson);
				const updateShipmentStorageId = await shipmentModel.updateShipmentStorageId(checkShipmentAssignToStorage.shipment_job_id, AddShipmentJson.data.data.id)
			}
		}

		const shipmentStageDetail = await shipmentTypeForShipmentModel.editShipmentStage(
			request.body
		);

		if (request.body.updateOtherStageDetails) {
			const updateOtherStageDetails = await shipmentTypeForShipmentModel.updateOtherShipmentDetails(
				{ local_shipment_stage_id: request.body.updateOtherStageDetails[0].local_shipment_stage_id },
				{
					scan_require: request.body.updateOtherStageDetails[0].scan_require,
					scan_out_of_storage: request.body.updateOtherStageDetails[0].scan_out_of_storage,
				}
			);
		}

		if (nextAdditionalStageId) {
			await shipmentTypeForShipmentModel.deleteShipmentTypeStageModel(nextAdditionalStageId)
			const shipmentStageDetail = await shipmentTypeForShipmentModel.viewShipmentStage(
				request.body.local_shipment_stage_id
			);
			const shipmentTypeDetail = await shipmentTypeForShipmentModel.fetchShipmentTypegForShipmentStagesForCompnay(shipmentStageDetail.dataValues.shipment_job_id);

			await shipmentTypeForShipmentModel.shipmentTypeStageDetailLengthUpdate(shipmentStageDetail.dataValues.local_shipment_stage_id, shipmentTypeDetail.length)

			async function deleteAndReorder(array, orderToDelete) {
				const indexToDelete = array.findIndex(item => item.order_of_stages === orderToDelete);
				if (indexToDelete !== -1) {
					array.splice(indexToDelete, 1);
					for (let i = indexToDelete; i < array.length; i++) {
						array[i].order_of_stages -= 1;
						await shipmentTypeForShipmentModel.updateStageOrderNumberShipmentTypeStage(array[i].local_shipment_stage_id, array[i].order_of_stages)
					}
				}
			}
			deleteAndReorder(shipmentTypeDetail, request.body.order_of_stages);
		}

		if (shipmentStageDetail) {
			// Get old data before update
			const oldShipmentStageData = await shipmentTypeForShipmentModel.viewShipmentStage(
				request.body.local_shipment_stage_id
			);

			// Capture both old and new values for UPDATE operation
			const affectedFields = ActionLogModel.captureUpdateFields(oldShipmentStageData, request.body);

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "SHIPMENT_TYPE_STAGE_OF_SHIPMENT_UPDATE",
				action_performed_on_id: request.body.local_shipment_stage_id,
				action_performed_on_name: request.body.name,
				affected_fields: affectedFields
			})
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: SHIPMENT_STAGE_UPDATE_SUCCESS,
				data: {},
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: SHIPMENT_STAGE_UPDATE_FAIL,
				data: {},
			});
		}

	} catch (error) {
		console.log("exports.editShipmentStage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};


exports.copyIsOverrideStageForShipmentInventory = async (request, response) => {
	try {
		const getAllOverrideInventory = await shipmentTypeForShipmentModel.getAllOverrideInventory();
		if (getAllOverrideInventory !== "") {
			for (let y = 0; y < getAllOverrideInventory.rows.length; y++) {
				const getAllShipmentStages = await shipmentTypeForShipmentModel.getAllShipmentStages(getAllOverrideInventory.rows[y].shipment_job_id);
				for (let i = 0; i < getAllShipmentStages.rows.length; i++) {
					if (getAllOverrideInventory.rows[y].shipment_job_id == getAllShipmentStages.rows[i].shipment_job_id && getAllOverrideInventory.rows[y].current_stage_id == getAllShipmentStages.rows[i].ref_shipment_stage_id) {
						const upOverrideShipmentStageUpdate = await shipmentTypeForShipmentModel.upOverrideShipmentStageUpdate(getAllOverrideInventory.rows[y].forced_status_id, getAllShipmentStages.rows[i].local_shipment_stage_id);
					}
				}
			}
			response.status(SUCCESS_CODE).json({
				message: SHIPMENT_STAGE_RETRIEVED_SUCCESS,
				data: "Done",
			});

		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: SHIPMENT_STAGE_NOT_FOUND,
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.copyIsOverrideStageForShipmentInventory -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}


exports.copyShipmentForcedDetails = async (request, response) => {
	try {
		const getAllShipmentForcedList = await shipmentTypeForShipmentModel.getAllShipmentForcedList();
		if (getAllShipmentForcedList !== "") {
			for (let y = 0; y < getAllShipmentForcedList.rows.length; y++) {
				const getAllShipmentStages = await shipmentTypeForShipmentModel.getAllShipmentStages(getAllShipmentForcedList.rows[y].shipment_job_id);
				for (let i = 0; i < getAllShipmentStages.rows.length; i++) {
					if (getAllShipmentForcedList.rows[y].shipment_job_id == getAllShipmentStages.rows[i].shipment_job_id && getAllShipmentForcedList.rows[y].current_stage_id == getAllShipmentStages.rows[i].ref_shipment_stage_id) {
						const upShipmentForcedUpdatelocal = await shipmentTypeForShipmentModel.upShipmentForcedUpdatelocal(getAllShipmentForcedList.rows[y].forced_status_id, getAllShipmentStages.rows[i].local_shipment_stage_id);
					}
				}

				for (let i = 0; i < getAllShipmentStages.rows.length; i++) {
					if (getAllShipmentForcedList.rows[y].shipment_job_id == getAllShipmentStages.rows[i].shipment_job_id && getAllShipmentForcedList.rows[y].altered_stage_id == getAllShipmentStages.rows[i].ref_shipment_stage_id) {
						const upShipmentForcedUpdateAlter = await shipmentTypeForShipmentModel.upShipmentForcedUpdateAlter(getAllShipmentForcedList.rows[y].forced_status_id, getAllShipmentStages.rows[i].local_shipment_stage_id);
					}
				}

			}
			response.status(SUCCESS_CODE).json({
				message: SHIPMENT_STAGE_RETRIEVED_SUCCESS,
				data: "Done",
			});

		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: SHIPMENT_STAGE_NOT_FOUND,
				data: {},
			});
		}
	}

	catch (error) {
		console.log("exports.copyShipmentForcedDetails -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}


exports.copyShipmentSignatureDetails = async (request, response) => {
	try {
		const getAllShipmentSignatureList = await shipmentTypeForShipmentModel.getAllShipmentSignatureList();
		if (getAllShipmentSignatureList !== "") {
			for (let y = 0; y < getAllShipmentSignatureList.rows.length; y++) {
				const getAllShipmentStages = await shipmentTypeForShipmentModel.getAllShipmentStages(getAllShipmentSignatureList.rows[y].shipment_job_id);
				for (let i = 0; i < getAllShipmentStages.rows.length; i++) {
					if (getAllShipmentSignatureList.rows[y].shipment_job_id == getAllShipmentStages.rows[i].shipment_job_id && getAllShipmentSignatureList.rows[y].stage == getAllShipmentStages.rows[i].ref_shipment_stage_id) {
						const upShipmentSingatureUpdate = await shipmentTypeForShipmentModel.upShipmentSingatureUpdate(getAllShipmentSignatureList.rows[y].shipment_job_signature_id, getAllShipmentStages.rows[i].local_shipment_stage_id);
					}
				}
			}
			response.status(SUCCESS_CODE).json({
				message: SHIPMENT_STAGE_RETRIEVED_SUCCESS,
				data: "Done",
			});

		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: SHIPMENT_STAGE_NOT_FOUND,
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.copyShipmentSignatureDetails -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}