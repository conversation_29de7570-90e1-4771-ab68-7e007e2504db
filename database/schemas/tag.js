"use strict";
module.exports = (sequelize, DataTypes) => {
	const tag = sequelize.define(
		"tag",
		{
			tag_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.TEXT,
			createdByUserRole: DataTypes.STRING,
			admin_id:
			{
				type: DataTypes.INTEGER,
			},
			staff_id: {
				type: DataTypes.INTEGER,
			},
			color: DataTypes.STRING(100),
			company_id: DataTypes.INTEGER,
			tag_for: {
				type: DataTypes.ENUM("CUSTOMER", "SHIPMENT", "ITEM"),
				defaultValue: "SHIPMENT",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false, freezeTableName: true }
	);
	tag.associate = function (models) {

		tag.belongsTo(models.company, {
			foreignKey: "company_id",
			as: "tag_company",
		});
		tag.belongsTo(models.tag_shipment, {
			foreignKey: "tag_id",
			as: "tag_shipment",
		});
		tag.belongsTo(models.tag_customer, {
			foreignKey: "tag_id",
			as: "tag_customer",
		});
		tag.belongsTo(models.tag_item, {
			foreignKey: "tag_id",
			as: "tag_item",
		});
	};
	return tag;
};
