"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_exception = sequelize.define(
		"shipment_exception",
		{
			shipment_exception_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_exception.associate = function (models) {
		// associations can be defined here
		shipment_exception.hasMany(models.shipment_inventory_exception, {
			foreignKey: "shipment_exception_id",
		});
	};
	return shipment_exception;
};
