"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_location = sequelize.define(
		"shipment_location",
		{
			shipment_location_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_location.associate = function (models) {
		// associations can be defined here
		shipment_location.hasMany(models.shipment_inventory_location, {
			foreignKey: "shipment_location_id",
		});
	};
	return shipment_location;
};
