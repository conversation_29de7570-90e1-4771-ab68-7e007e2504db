"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_room = sequelize.define(
		"shipment_room",
		{
			shipment_room_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			createdByUserRole: DataTypes.STRING,

			admin_id:
			{
				type: DataTypes.INTEGER,
			},
			company_id: {
				type: DataTypes.INTEGER,
			},
			staff_id: {
				type: DataTypes.INTEGER,
			},

			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_room.associate = function (models) {
		// associations can be defined here
		shipment_room.hasMany(models.shipment_inventory, {
			foreignKey: "room_id",
		});
	};
	return shipment_room;
};
