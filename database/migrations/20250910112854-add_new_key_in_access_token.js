"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'accesstokens',
        'name',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("accesstokens", "name");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};