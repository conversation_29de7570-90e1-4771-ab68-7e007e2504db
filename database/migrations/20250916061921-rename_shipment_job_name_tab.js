"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.renameColumn("tag_shipment", "shipment_id", "shipment_job_id");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.renameColumn("tag_shipment", "shipment_job_id", "shipment_id");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
